
const Header = () => {
  return (
    <header className="bg-gradient-to-r from-blue-900 to-blue-700 text-white pt-16" style={{background: 'linear-gradient(to right, #1e3a8a, #1d4ed8)'}}>
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="text-center">
          <h1 className="text-5xl font-bold mb-4">YOUSEF RABIE KHALIL</h1>
          <h2 className="text-2xl font-light mb-6 text-blue-100">
            Senior Backend Developer | Node.js | API Development | Integration Specialist
          </h2>

          <div className="flex flex-wrap justify-center gap-6 text-sm">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center gap-2 hover:text-blue-200 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
              </svg>
              <EMAIL>
            </a>

            <a
              href="tel:+201017931092"
              className="flex items-center gap-2 hover:text-blue-200 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
              </svg>
              +201017931092
            </a>

            <a
              href="https://www.linkedin.com/in/yousef-rabie-khalil/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 hover:text-blue-200 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd"/>
              </svg>
              LinkedIn
            </a>

            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
              </svg>
              Cairo, Egypt
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
