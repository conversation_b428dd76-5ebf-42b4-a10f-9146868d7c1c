import { useEffect, useState } from 'react'

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState('summary')

  const navItems = [
    { id: 'summary', label: 'Summary' },
    { id: 'experience', label: 'Experience' },
    { id: 'projects', label: 'Projects' },
    { id: 'skills', label: 'Skills' },
    { id: 'achievements', label: 'Achievements' },
    { id: 'education', label: 'Education' },
    { id: 'contact', label: 'Contact' }
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100)

      // Update active section based on scroll position
      const sections = navItems.map(item => document.getElementById(item.id))
      const scrollPosition = window.scrollY + 200

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i]
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(navItems[i].id)
          break
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <nav className="fixed top-8 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300">
      <div className="flex items-center space-x-0 bg-white bg-opacity-10 backdrop-blur-md rounded-full border border-white border-opacity-20 overflow-hidden">
        <button
          onClick={() => scrollToSection('skills')}
          className="text-white hover:text-gray-300 transition-colors font-semibold px-6 py-3 text-sm"
        >
          Skills
        </button>
        <button
          onClick={() => scrollToSection('summary')}
          className="text-white hover:text-gray-300 transition-colors font-semibold px-6 py-3 text-sm"
        >
          About me
        </button>
        <button
          onClick={() => scrollToSection('projects')}
          className="text-white hover:text-gray-300 transition-colors font-semibold px-6 py-3 text-sm"
        >
          Portfolio
        </button>
        <button
          onClick={() => scrollToSection('contact')}
          className="bg-white text-black px-6 py-3 hover:bg-gray-100 transition-colors font-bold text-sm"
        >
          CONTACT ME
        </button>
      </div>
    </nav>
  )
}

export default Navigation
