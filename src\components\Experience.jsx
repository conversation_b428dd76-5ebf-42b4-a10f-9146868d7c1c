import React from 'react'

const Experience = () => {
  const experiences = [
    {
      title: "Senior Backend Engineer and Integration Specialist",
      company: "ENABLE, EButler",
      period: "2023 - Ongoing",
      location: "Remotely",
      description: [
        "Developed ENABLE, an all-in-one platform for businesses to establish an online presence, including an eCommerce Website Builder, CRM, OMS, Delivery Management, Payment Gateway, and Restaurant Management System",
        "Utilized Node.js (Nestjs framework), MongoDb MongoAtlas Cloud, Nginx, and Angular technologies to create a seamless and efficient experience for businesses"
      ]
    },
    {
      title: "Senior Software Engineer",
      company: "Enable, EButler",
      period: "05/2020 - 05/2023",
      location: "Remotely",
      description: [
        "This plugin aims to streamline WooCommerce functionality by combining the features of Enable's payment gateway and order management system into a single, cohesive solution",
        "Seamlessly process payments through Enable's gateway directly within WooCommerce",
        "Manage all orders, including payment status, shipping details, and customer information, from a central interface",
        "Track order progress in real-time, from order placement to delivery"
      ]
    },
    {
      title: "Senior Backend Engineer (Part Time)",
      company: "Medhubeg",
      period: "07/2024 - Ongoing",
      location: "Cairo",
      description: [
        "Built Medhub back-end from scratch using Node.js, Nestjs, Typeorm, and Postgresql",
        "Delivered comprehensive and well-organized API documentation",
        "Organized and prioritized work to complete assignments efficiently and on time"
      ]
    },
    {
      title: "Senior Software Engineer (Part Time)",
      company: "Baeynh",
      period: "12/2022 - 12/2023",
      location: "Remotely",
      description: [
        "Built Baeynh back-end from scratch using Node.js, Nestjs, Sequalize, and MySQL",
        "Delivered comprehensive and well-organized API documentation",
        "Organized and prioritized work to complete assignments efficiently and on time"
      ]
    },
    {
      title: "Full stack developer",
      company: "Edge Technology",
      period: "09/2019 - 05/2020",
      location: "Dokki",
      description: [
        "Utilized Node.js to build and develop scalable and efficient e-commerce and marketplace projects, including Boq",
        "Organized and prioritized work to complete assignments efficiently and on time"
      ]
    },
    {
      title: "Full stack developer",
      company: "Dopave",
      period: "08/2018 - 09/2019",
      location: "Dopave, dokki",
      description: [
        "Experienced in building Angular projects as a front-end developer",
        "Proficient in using Angular and lonic as front-end frameworks, and Sails.js as a back-end framework",
        "Worked on various projects, including Vexpress, Careema, and Alaunna, utilizing these technologies"
      ]
    },
    {
      title: "Mobile application developer",
      company: "7ds, Elmaddi",
      period: "08/2017 - 08/2018",
      location: "7ds, Elmaddi",
      description: [
        "Experienced in using Cloud, Mobile, and Web technologies for complex problem-solving",
        "Proficient in developing and releasing mobile applications using the lonic framework",
        "Worked on various projects, including 365Deal and Foundira, utilizing these technologies"
      ]
    }
  ]

  return (
    <section className="mb-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 border-b-2 border-blue-600 pb-2">
          Experience
        </h2>
        
        <div className="space-y-8">
          {experiences.map((exp, index) => (
            <div key={index} className="relative pl-8 border-l-2 border-blue-200">
              <div className="absolute w-4 h-4 bg-blue-600 rounded-full -left-2 top-0"></div>
              
              <div className="mb-4">
                <h3 className="text-xl font-bold text-gray-800">{exp.title}</h3>
                <div className="flex flex-wrap gap-4 text-sm text-gray-600 mt-1">
                  <span className="font-semibold text-blue-600">{exp.company}</span>
                  <span>{exp.period}</span>
                  <span>{exp.location}</span>
                </div>
              </div>
              
              <ul className="space-y-2">
                {exp.description.map((item, idx) => (
                  <li key={idx} className="text-gray-700 flex items-start">
                    <span className="text-blue-600 mr-2 mt-1">•</span>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Experience
