/* Custom styles for the CV application */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    font-size: 11px;
    line-height: 1.4;
  }

  .container {
    max-width: none !important;
    padding: 0 !important;
  }

  .shadow-lg {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }

  .bg-gradient-to-r {
    background: #1e40af !important;
    color: white !important;
  }

  .bg-gray-50 {
    background: white !important;
  }

  /* Hide navigation and scroll button when printing */
  nav, .fixed {
    display: none !important;
  }

  /* Adjust header for print */
  header {
    padding-top: 0 !important;
  }

  /* Ensure sections break properly */
  section {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Adjust spacing for print */
  .mb-12 {
    margin-bottom: 1.5rem !important;
  }

  .py-8 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

/* Smooth animations */
.transition-all {
  transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
