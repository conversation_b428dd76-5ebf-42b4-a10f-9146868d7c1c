import React from 'react'

const Projects = () => {
  const projects = [
    {
      title: "Enable – SaaS Platform for Online Business",
      period: "2020 - Ongoing",
      company: "Enable - EButler",
      url: "https://enable.tech/",
      description: "All‑in‑one SaaS platform that lets merchants set up web stores, manage CRM, orders, payments, delivery, and restaurant POS from a single dashboard.",
      highlights: [
        "Multi‑tenant e‑commerce builder, CRM, OMS, payments, delivery & restaurant POS",
        "Processes 200K+ orders per month for 400+ merchants"
      ],
      tech: ["Node.js", "NestJS", "MongoDB Atlas", "Kafka", "Angular", "Kubernetes"]
    },
    {
      title: "Enable Payments & Orders – WooCommerce Plugin",
      period: "01/2023 - 12/2023",
      company: "EButler - Enable",
      description: "WordPress plugin that fuses Enable's payment gateway and OMS into WooCommerce for frictionless checkout and fulfilment.",
      highlights: [
        "Adopted by 120+ merchants within 3 months of launch"
      ],
      tech: ["PHP", "WooCommerce", "WordPress", "NestJS API"]
    },
    {
      title: "Doha Festival City Marketplace",
      period: "01/2024 - 12/2024",
      company: "EButler",
      description: "Online marketplace unifying 400+ retailers inside Qatar's largest mall with sub‑second product search.",
      highlights: [
        "Enhanced MedusaJS backend; added Algolia search & Redis caching → < 100 ms search"
      ],
      tech: ["MedusaJS", "PostgreSQL", "Redis", "Algolia", "Docker"]
    },
    {
      title: "Tenx Property Management",
      period: "01/2023 - 07/2024",
      company: "EButler",
      description: "Multi‑tenant SaaS for property owners and FM companies to manage compounds, amenities, and maintenance requests.",
      highlights: [
        "Designed CQRS & event‑sourced architecture for auditability and scalability"
      ],
      tech: ["Node.js", "MongoDB", "Kafka", "NestJS", "Angular"]
    },
    {
      title: "Medhub Health Marketplace",
      period: "2024 - Ongoing",
      company: "Medhub",
      url: "https://play.google.com/store/apps/details?id=com.medhubeg&hl=en",
      description: "B2B/B2C platform connecting clinics, pharmacies, and patients with streamlined appointment and procurement flows.",
      highlights: [
        "Backend & API delivered with full OpenAPI docs 2 weeks ahead of schedule"
      ],
      tech: ["NestJS", "TypeORM", "PostgreSQL", "Docker", "Swagger"]
    },
    {
      title: "Baeynh Legal Services Platform",
      period: "01/2022 - 03/2023",
      company: "Baeynh",
      description: "Digital marketplace that matches users with licensed lawyers and legal advisors across MENA.",
      highlights: [
        "RBAC and encryption meet regional data‑privacy regulations; serves 50K+ users"
      ],
      tech: ["NestJS", "Sequelize", "MySQL", "AWS S3"]
    },
    {
      title: "Qareeb Plus – Hyperlocal Directory for New Obour",
      period: "2025 - Ongoing",
      company: "Founder/Freetime",
      url: "https://qareeb-plus.web.app/",
      description: "Community platform that lets New Obour residents instantly locate nearby shops, markets, and services via searchable map and category filters.",
      highlights: [
        "Built React + Vite SPA with NestJS REST API and PostgreSQL; achieved 96 Lighthouse Performance score",
        "Onboarded 30+ local businesses in first 60 days, reaching 300 monthly active visitors through word‑of‑mouth"
      ],
      tech: ["React", "Vite", "NestJS", "PostgreSQL", "Docker"]
    }
  ]

  return (
    <section className="mb-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 border-b-2 border-blue-600 pb-2">
          Featured Projects
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {projects.map((project, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="mb-4">
                <h3 className="text-xl font-bold text-gray-800 mb-2">
                  {project.url ? (
                    <a href={project.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                      {project.title} ↗
                    </a>
                  ) : (
                    project.title
                  )}
                </h3>
                <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                  <span className="font-semibold text-blue-600">{project.company}</span>
                  <span>•</span>
                  <span>{project.period}</span>
                </div>
              </div>
              
              <p className="text-gray-700 mb-4">{project.description}</p>
              
              {project.highlights && (
                <ul className="mb-4 space-y-1">
                  {project.highlights.map((highlight, idx) => (
                    <li key={idx} className="text-sm text-gray-600 flex items-start">
                      <span className="text-green-600 mr-2 mt-1">✓</span>
                      <span>{highlight}</span>
                    </li>
                  ))}
                </ul>
              )}
              
              <div className="flex flex-wrap gap-2">
                {project.tech.map((tech, idx) => (
                  <span key={idx} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Projects
