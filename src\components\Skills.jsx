
const Skills = () => {
  const skillCategories = [
    {
      title: "Backend & APIs",
      skills: ["NestJS", "Sails.js", "Express.js", "Deno", "Bun", "ASP.NET Core", "REST APIs"]
    },
    {
      title: "Databases",
      skills: ["PostgreSQL", "MySQL", "MongoDB"]
    },
    {
      title: "DevOps & Cloud",
      skills: ["Docker", "Kubernetes", "Jenkins", "GitHub Actions", "AWS", "GCP", "Azure"]
    },
    {
      title: "Messaging & Streaming",
      skills: ["Kafka", "RabbitMQ", "NATS"]
    },
    {
      title: "eCommerce & Integrations",
      skills: ["Shopify", "WooCommerce", "MedusaJS", "Deliverect", "Tookan"]
    },
    {
      title: "Frontend & Mobile",
      skills: ["Angular", "Ionic", "React"]
    },
    {
      title: "Practices & Methodologies",
      skills: ["TDD", "Clean Architecture", "Domain‑Driven Design", "Agile (Scrum)"]
    }
  ]

  const getSkillColor = (category) => {
    const colors = {
      "Backend & APIs": "bg-blue-100 text-blue-800",
      "Databases": "bg-green-100 text-green-800",
      "DevOps & Cloud": "bg-purple-100 text-purple-800",
      "Messaging & Streaming": "bg-orange-100 text-orange-800",
      "eCommerce & Integrations": "bg-pink-100 text-pink-800",
      "Frontend & Mobile": "bg-indigo-100 text-indigo-800",
      "Practices & Methodologies": "bg-gray-100 text-gray-800"
    }
    return colors[category] || "bg-gray-100 text-gray-800"
  }

  return (
    <section className="mb-12">
      <h2 className="section-title">Skills & Technologies</h2>
      <div className="section">

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {skillCategories.map((category, index) => (
            <div key={index} className="skill-category bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-800">{category.title}</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {category.skills.map((skill, idx) => (
                  <span
                    key={idx}
                    className={`skill-badge px-4 py-2 rounded-full text-sm font-semibold ${getSkillColor(category.title)} border border-opacity-20`}
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-blue-800 mb-4">Languages</h3>
          <div className="flex gap-4">
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-blue-600 rounded-full"></span>
              <span className="text-gray-700">Arabic (Native)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-green-600 rounded-full"></span>
              <span className="text-gray-700">English (Professional)</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Skills
