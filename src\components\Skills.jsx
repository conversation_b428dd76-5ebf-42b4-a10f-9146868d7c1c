import React from 'react'

const Skills = () => {
  const skillCategories = [
    {
      title: "Backend & APIs",
      skills: ["NestJS", "Sails.js", "Express.js", "Deno", "Bun", "ASP.NET Core", "REST APIs"]
    },
    {
      title: "Databases",
      skills: ["PostgreSQL", "MySQL", "MongoDB"]
    },
    {
      title: "DevOps & Cloud",
      skills: ["Docker", "Kubernetes", "Jenkins", "GitHub Actions", "AWS", "GCP", "Azure"]
    },
    {
      title: "Messaging & Streaming",
      skills: ["Kafka", "RabbitMQ", "NATS"]
    },
    {
      title: "eCommerce & Integrations",
      skills: ["Shopify", "WooCommerce", "MedusaJS", "Deliverect", "Tookan"]
    },
    {
      title: "Frontend & Mobile",
      skills: ["Angular", "Ionic", "React"]
    },
    {
      title: "Practices & Methodologies",
      skills: ["TDD", "Clean Architecture", "Domain‑Driven Design", "Agile (Scrum)"]
    }
  ]

  const getSkillColor = (category) => {
    const colors = {
      "Backend & APIs": "bg-blue-100 text-blue-800",
      "Databases": "bg-green-100 text-green-800",
      "DevOps & Cloud": "bg-purple-100 text-purple-800",
      "Messaging & Streaming": "bg-orange-100 text-orange-800",
      "eCommerce & Integrations": "bg-pink-100 text-pink-800",
      "Frontend & Mobile": "bg-indigo-100 text-indigo-800",
      "Practices & Methodologies": "bg-gray-100 text-gray-800"
    }
    return colors[category] || "bg-gray-100 text-gray-800"
  }

  return (
    <section className="mb-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 border-b-2 border-blue-600 pb-2">
          Technical Skills
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {skillCategories.map((category, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">{category.title}</h3>
              <div className="flex flex-wrap gap-2">
                {category.skills.map((skill, idx) => (
                  <span 
                    key={idx} 
                    className={`px-3 py-1 rounded-full text-sm font-medium ${getSkillColor(category.title)}`}
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-blue-800 mb-4">Languages</h3>
          <div className="flex gap-4">
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-blue-600 rounded-full"></span>
              <span className="text-gray-700">Arabic (Native)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-green-600 rounded-full"></span>
              <span className="text-gray-700">English (Professional)</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Skills
