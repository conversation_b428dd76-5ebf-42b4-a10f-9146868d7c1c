
const Skills = () => {
  const skillCategories = [
    {
      title: "USING NOW:",
      skills: ["HTML5", "CSS3", "SASS", "JAVASCRIPT", "REACT", "BOOTSTRAP", "GIT", "FIGMA"]
    },
    {
      title: "LEARNING:",
      skills: ["TYPESCRIPT", "NODEJS", "MySQL", "MONGODB"]
    },
    {
      title: "OTHER SKILLS:",
      skills: ["C", "C++", "ANGIELSKI C1/C2", "HISZPAŃSKI B1/B2"]
    }
  ]

  return (
    <section id="skills" className="section">
      <div className="container mx-auto px-4 max-w-6xl">
        <h2 className="section-title">SKILLS</h2>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mt-16">
          {skillCategories.map((category, index) => (
            <div key={index} className="skill-category">
              <h3 className="text-sm font-bold text-white mb-8 tracking-wider">
                {category.title}
              </h3>

              <div className="space-y-6">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="flex flex-col items-center text-center">
                    {/* Technology Icon Placeholder */}
                    <div className="w-16 h-16 bg-gray-700 rounded-lg mb-3 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">
                        {skill.substring(0, 2)}
                      </span>
                    </div>

                    {/* Skill Name */}
                    <span className="text-white text-xs font-medium tracking-wider">
                      {skill}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills
