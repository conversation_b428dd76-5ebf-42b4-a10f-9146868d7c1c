import React from 'react'

const Education = () => {
  return (
    <section className="mb-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 border-b-2 border-blue-600 pb-2">
          Education
        </h2>
        
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
          <div className="flex items-start gap-4">
            <div className="text-3xl">🎓</div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Bachelor of Computer Science</h3>
              <div className="flex flex-wrap gap-4 text-gray-600 mb-4">
                <span className="font-semibold text-blue-600">Ain Shams University</span>
                <span>•</span>
                <span>06/2013 - 07/2017</span>
                <span>•</span>
                <span>Cairo, Egypt</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="text-gray-700">Graduated with <span className="font-semibold text-green-600">Very Good</span> grade</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="text-gray-700">Developed a <span className="font-semibold">voice-based C++ editor</span> as graduation project</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span className="text-gray-700">Participated in <span className="font-semibold">ACM</span> and <span className="font-semibold">Support F1</span> activities</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Education
