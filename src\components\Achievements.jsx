import React from 'react'

const Achievements = () => {
  const achievements = [
    {
      title: "Enhanced Workflow Efficiency",
      description: "Increased software deployment speed by 40% through efficient workflow enhancements.",
      icon: "⚡"
    },
    {
      title: "Marketplace Platforms Developer",
      description: "Developed 5 successful marketplace platforms, enhancing sales by 30% on average.",
      icon: "🏪"
    },
    {
      title: "Plugin Developer Success",
      description: "Created a WooCommerce, Shopify plugin used by 500+ businesses, improving transaction processing by 50%.",
      icon: "🔌"
    },
    {
      title: "Enhanced Platform Uptime",
      description: "Increased e-commerce platform uptime by 99.9% using advanced monitoring and alerts.",
      icon: "📈"
    },
    {
      title: "API Integration Success",
      description: "Successfully integrated 10 third-party APIs, improving system compatibility by 50%.",
      icon: "🔗"
    },
    {
      title: "Enhanced Team Productivity",
      description: "Increased team productivity by 25% by implementing effective mentorship programs.",
      icon: "👥"
    },
    {
      title: "Maximized System Uptime",
      description: "Achieved 99.9% uptime by implementing robust DevOps practices and monitoring tools.",
      icon: "🛡️"
    }
  ]

  const awards = [
    {
      title: "WebSummit Lisbon Participant",
      year: "2021 & 2022",
      description: "Selected among 10K+ applicants for WebSummit Lisbon"
    }
  ]

  return (
    <section className="mb-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 border-b-2 border-blue-600 pb-2">
          Achievements & Awards
        </h2>
        
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-6">Key Achievements</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {achievements.map((achievement, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
                <div className="text-3xl mb-3">{achievement.icon}</div>
                <h4 className="text-lg font-bold text-gray-800 mb-2">{achievement.title}</h4>
                <p className="text-gray-600 text-sm">{achievement.description}</p>
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <h3 className="text-xl font-bold text-gray-800 mb-6">Awards & Recognition</h3>
          <div className="space-y-4">
            {awards.map((award, index) => (
              <div key={index} className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-start gap-4">
                  <div className="text-2xl">🏆</div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-800">{award.title}</h4>
                    <p className="text-yellow-700 font-semibold">{award.year}</p>
                    <p className="text-gray-600 mt-1">{award.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Achievements
